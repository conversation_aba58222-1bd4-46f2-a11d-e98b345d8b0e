# Hướng Dẫn Triển Khai EWH Landing Zone Qua Azure Portal

Tài liệu này hướng dẫn triển khai EWH Azure Landing Zone thông qua Azure Portal thay vì sử dụng Terraform CLI, dựa trên kiến trúc Microsoft Cloud Adoption Framework (CAF) Enterprise Scale.

## 🏗️ Tổng Quan Kiến Trúc

Landing zone triển khai mô hình mạng hub-and-spoke với các thành phần sau:

- **Management Groups**: Cấu trúc phân cấp quản lý subscription
- **Azure Policies**: Chính sách tuân thủ và bảo mật
- **Hub Virtual Network**: Mạng trung tâm với Azure Firewall
- **Log Analytics Workspace**: Giám sát tập trung
- **Microsoft Defender for Cloud**: Bảo mật và tuân thủ
- **Spoke Networks**: Mạng ứng dụng kết nối với hub

## 📋 Yêu Cầu Tiên Quyết

- **Azure Subscription**: Ít nhất 1 subscription (khuyến nghị 3 subscriptions riêng biệt)
- **Quyền Azure**: 
  - Owner hoặc User Access Administrator ở cấp tenant
  - Contributor ở cấp subscription
  - Privileged Role Administrator (nếu cần tạo Azure AD groups)
- **Trình duyệt web**: Microsoft Edge, Chrome, hoặc Firefox
- **Azure CLI** (tùy chọn): Để thực hiện một số tác vụ nâng cao

## 🎯 Kế Hoạch Triển Khai

### Giai Đoạn 1: Chuẩn Bị Môi Trường (30 phút)
1. Xác định subscription IDs
2. Chuẩn bị thông tin cấu hình
3. Thiết lập quyền truy cập

### Giai Đoạn 2: Tạo Management Groups (45 phút)
1. Tạo root management group
2. Tạo các management groups con
3. Di chuyển subscriptions

### Giai Đoạn 3: Cấu Hình Policies (60 phút)
1. Gán built-in policies
2. Tạo custom policies (nếu cần)
3. Cấu hình policy assignments

### Giai Đoạn 4: Triển Khai Management Resources (45 phút)
1. Tạo Log Analytics workspace
2. Cấu hình Microsoft Defender for Cloud
3. Thiết lập monitoring

### Giai Đoạn 5: Triển Khai Hub Network (60 phút)
1. Tạo hub virtual network
2. Cấu hình Azure Firewall
3. Thiết lập routing

### Giai Đoạn 6: Triển Khai Spoke Networks (30 phút/spoke)
1. Tạo spoke virtual networks
2. Cấu hình peering
3. Thiết lập network security groups

## 🚀 Bước 1: Chuẩn Bị Môi Trường

### 1.1 Thu Thập Thông Tin Cần Thiết

Chuẩn bị các thông tin sau:

```
Thông tin tổ chức:
- Root ID: ewh
- Root Name: EWH
- Primary Location: Southeast Asia
- Secondary Location: East Asia

Subscription IDs:
- Management Subscription: xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx
- Connectivity Subscription: xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx
- Identity Subscription: xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx

Thông tin liên hệ:
- Security Contact Email: <EMAIL>
- IT Team Email: <EMAIL>
```

### 1.2 Kiểm Tra Quyền Truy Cập

1. Đăng nhập vào [Azure Portal](https://portal.azure.com)
2. Kiểm tra quyền tại **Azure Active Directory** > **Roles and administrators**
3. Xác nhận có quyền **Owner** hoặc **User Access Administrator**

## 🏢 Bước 2: Tạo Management Groups

### 2.1 Tạo Root Management Group

1. Trong Azure Portal, tìm kiếm **Management groups**
2. Click **+ Create**
3. Điền thông tin:
   - **Management group ID**: `ewh`
   - **Management group display name**: `EWH`
   - **Parent management group**: Tenant Root Group
4. Click **Submit**

### 2.2 Tạo Platform Management Groups

Tạo các management groups con theo cấu trúc:

#### 2.2.1 Platform Management Group
- **ID**: `ewh-platform`
- **Display name**: `Platform`
- **Parent**: `ewh`

#### 2.2.2 Management Management Group
- **ID**: `ewh-management`
- **Display name**: `Management`
- **Parent**: `ewh-platform`

#### 2.2.3 Connectivity Management Group
- **ID**: `ewh-connectivity`
- **Display name**: `Connectivity`
- **Parent**: `ewh-platform`

#### 2.2.4 Identity Management Group
- **ID**: `ewh-identity`
- **Display name**: `Identity`
- **Parent**: `ewh-platform`

### 2.3 Tạo Landing Zones Management Groups

#### 2.3.1 Landing Zones Root
- **ID**: `ewh-landing-zones`
- **Display name**: `Landing Zones`
- **Parent**: `ewh`

#### 2.3.2 Production Landing Zone
- **ID**: `ldz-ewh-prd`
- **Display name**: `Production`
- **Parent**: `ewh-landing-zones`

#### 2.3.3 Production Sub-groups
Tạo các sub-groups cho Production:

**Legacy Applications:**
- **ID**: `ldz-ewh-prd-legacy`
- **Display name**: `Legacy`
- **Parent**: `ldz-ewh-prd`

**Microservices Applications:**
- **ID**: `ldz-ewh-prd-microsvc`
- **Display name**: `Microservices`
- **Parent**: `ldz-ewh-prd`

#### 2.3.4 Non-Production Landing Zone
- **ID**: `ldz-ewh-non-prd`
- **Display name**: `Non-Production`
- **Parent**: `ewh-landing-zones`

#### 2.3.5 Non-Production Sub-groups
**UAT Environment:**
- **ID**: `ldz-ewh-non-prd-uat`
- **Display name**: `UAT`
- **Parent**: `ldz-ewh-non-prd`

**Development Environment:**
- **ID**: `ldz-ewh-non-prd-dev`
- **Display name**: `Development`
- **Parent**: `ldz-ewh-non-prd`

### 2.4 Di Chuyển Subscriptions

1. Trong **Management groups**, chọn management group đích
2. Click **+ Add subscription**
3. Chọn subscription cần di chuyển
4. Click **Save**

**Mapping subscriptions:**
- Management subscription → `ewh-management`
- Connectivity subscription → `ewh-connectivity`
- Identity subscription → `ewh-identity`

## 📋 Bước 3: Cấu Hình Azure Policies

### 3.1 Gán Built-in Policy Initiatives

#### 3.1.1 Tại Root Management Group (ewh)

1. Vào **Management groups** > chọn `ewh`
2. Click **Policies** > **Assignments** > **+ Assign initiative**
3. Gán các initiatives sau:

**Azure Security Benchmark:**
- **Initiative**: `Azure Security Benchmark`
- **Assignment name**: `ASB-EWH-Root`
- **Enforcement mode**: Enabled

**Configure Azure Defender:**
- **Initiative**: `Configure Azure Defender to be enabled on SQL servers and SQL Managed Instances`
- **Assignment name**: `Defender-SQL-EWH`
- **Enforcement mode**: Enabled

#### 3.1.2 Tại Platform Management Groups

**Cho Management Group (ewh-management):**
- **Initiative**: `Configure Log Analytics workspace and automation account to centralize logs and monitoring`
- **Assignment name**: `LogAnalytics-Management`

**Cho Connectivity Group (ewh-connectivity):**
- **Initiative**: `Configure Azure Firewall to enable threat intelligence`
- **Assignment name**: `Firewall-ThreatIntel`

### 3.2 Cấu Hình Tagging Policies

#### 3.2.1 Tạo Custom Policy cho Required Tags

1. Vào **Policy** > **Definitions** > **+ Policy definition**
2. Tạo policy yêu cầu các tags bắt buộc:

```json
{
  "mode": "All",
  "policyRule": {
    "if": {
      "anyOf": [
        {
          "field": "tags['Owner']",
          "exists": "false"
        },
        {
          "field": "tags['Organization']",
          "exists": "false"
        },
        {
          "field": "tags['Environment']",
          "exists": "false"
        },
        {
          "field": "tags['CostCenter']",
          "exists": "false"
        }
      ]
    },
    "then": {
      "effect": "deny"
    }
  }
}
```

3. Gán policy này tại root management group

## 🔧 Bước 4: Triển Khai Management Resources

### 4.1 Tạo Resource Group

1. Tìm kiếm **Resource groups** trong Azure Portal
2. Click **+ Create**
3. Điền thông tin:
   - **Subscription**: Management subscription
   - **Resource group name**: `rg-ewh-management-sea`
   - **Region**: `Southeast Asia`
   - **Tags**: Thêm các tags bắt buộc
4. Click **Review + create** > **Create**

### 4.2 Tạo Log Analytics Workspace

#### 4.2.1 Infrastructure Log Analytics Workspace

1. Tìm kiếm **Log Analytics workspaces**
2. Click **+ Create**
3. Cấu hình:
   - **Subscription**: Management subscription
   - **Resource group**: `rg-ewh-management-sea`
   - **Name**: `law-ewh-infrastructure-sea`
   - **Region**: `Southeast Asia`
   - **Pricing tier**: `Pay-as-you-go (Per GB 2018)`
4. Trong **Tags**, thêm:
   ```
   Owner: <EMAIL>
   Organization: EWH
   Environment: Production
   CostCenter: IT-001
   Purpose: Infrastructure Monitoring
   ```
5. Click **Review + create** > **Create**

#### 4.2.2 Security Log Analytics Workspace

Tương tự tạo workspace cho security:
- **Name**: `law-ewh-security-sea`
- **Purpose**: `Security Monitoring`

### 4.3 Cấu Hình Microsoft Defender for Cloud

1. Tìm kiếm **Microsoft Defender for Cloud**
2. Vào **Environment settings**
3. Chọn subscription management
4. Bật các Defender plans:
   - **Servers**: On
   - **App Service**: On
   - **Databases**: On
   - **Storage**: On
   - **Key Vault**: On
   - **Resource Manager**: On
   - **DNS**: On

### 4.4 Cấu Hình Security Contacts

1. Trong **Microsoft Defender for Cloud**
2. Vào **Environment settings** > chọn subscription
3. Click **Email notifications**
4. Cấu hình:
   - **Email addresses**: `<EMAIL>`
   - **Notify about alerts with the following severity**: High
   - **Send email notifications to subscription owners**: Yes

## 🌐 Bước 5: Triển Khai Hub Network

### 5.1 Tạo Hub Resource Group

1. Tạo resource group mới:
   - **Name**: `rg-ewh-connectivity-sea`
   - **Subscription**: Connectivity subscription
   - **Region**: `Southeast Asia`

### 5.2 Tạo Hub Virtual Network

1. Tìm kiếm **Virtual networks**
2. Click **+ Create**
3. Cấu hình **Basics**:
   - **Subscription**: Connectivity subscription
   - **Resource group**: `rg-ewh-connectivity-sea`
   - **Name**: `vnet-ewh-hub-sea`
   - **Region**: `Southeast Asia`

4. Cấu hình **IP Addresses**:
   - **IPv4 address space**: `**********/16`
   
5. Thêm subnets:
   - **GatewaySubnet**: `**********/24`
   - **AzureFirewallSubnet**: `**********/24`
   - **AzureBastionSubnet**: `**********/24`
   - **ManagementSubnet**: `***********/21`

6. Thêm tags và tạo

### 5.3 Triển Khai Azure Firewall

#### 5.3.1 Tạo Public IP cho Firewall

1. Tìm kiếm **Public IP addresses**
2. Click **+ Create**
3. Cấu hình:
   - **Name**: `pip-ewh-firewall-sea`
   - **SKU**: Standard
   - **Assignment**: Static
   - **Resource group**: `rg-ewh-connectivity-sea`

#### 5.3.2 Tạo Azure Firewall

1. Tìm kiếm **Firewalls**
2. Click **+ Create**
3. Cấu hình:
   - **Name**: `afw-ewh-hub-sea`
   - **Resource group**: `rg-ewh-connectivity-sea`
   - **Region**: `Southeast Asia`
   - **Firewall tier**: Standard
   - **Virtual network**: `vnet-ewh-hub-sea`
   - **Public IP address**: `pip-ewh-firewall-sea`

### 5.4 Cấu Hình Route Tables

1. Tạo route table cho spoke networks:
   - **Name**: `rt-ewh-spokes-sea`
   - **Resource group**: `rg-ewh-connectivity-sea`

2. Thêm route:
   - **Route name**: `default-via-firewall`
   - **Address prefix**: `0.0.0.0/0`
   - **Next hop type**: Virtual appliance
   - **Next hop address**: `**********` (Azure Firewall private IP)

## 🔗 Bước 6: Triển Khai Spoke Networks

### 6.1 Tạo Production Spoke

#### 6.1.1 Tạo Resource Group
- **Name**: `rg-ewh-prd-workload-sea`
- **Subscription**: Production subscription (hoặc management nếu chỉ có 1 subscription)

#### 6.1.2 Tạo Virtual Network
1. **Name**: `vnet-ewh-prd-workload-sea`
2. **Address space**: `**********/16`
3. **Subnets**:
   - **ApplicationSubnet**: `**********/24`
   - **DatabaseSubnet**: `**********/24`
   - **PrivateEndpointSubnet**: `**********/24`

#### 6.1.3 Cấu Hình VNet Peering

1. Trong hub VNet, vào **Peerings**
2. Click **+ Add**
3. Cấu hình:
   - **Peering link name**: `hub-to-prd-workload`
   - **Remote virtual network**: `vnet-ewh-prd-workload-sea`
   - **Allow gateway transit**: Checked
   - **Use remote gateways**: Unchecked

4. Cấu hình peering ngược từ spoke về hub:
   - **Peering link name**: `prd-workload-to-hub`
   - **Use remote gateways**: Checked (nếu có VPN Gateway)

### 6.2 Tạo Non-Production Spoke

Tương tự như production spoke:
- **VNet name**: `vnet-ewh-nonprod-workload-sea`
- **Address space**: `**********/16`
- **Resource group**: `rg-ewh-nonprod-workload-sea`

## 🔒 Bước 7: Cấu Hình Bảo Mật

### 7.1 Network Security Groups

#### 7.1.1 Tạo NSG cho Application Subnet

1. Tìm kiếm **Network security groups**
2. Click **+ Create**
3. Cấu hình:
   - **Name**: `nsg-ewh-prd-app-sea`
   - **Resource group**: `rg-ewh-prd-workload-sea`

4. Thêm inbound rules:
   - **HTTP**: Port 80, Source: VirtualNetwork
   - **HTTPS**: Port 443, Source: VirtualNetwork
   - **SSH**: Port 22, Source: Hub subnet only

#### 7.1.2 Gán NSG cho Subnet

1. Vào spoke VNet > **Subnets**
2. Chọn ApplicationSubnet
3. Trong **Network security group**, chọn `nsg-ewh-prd-app-sea`
4. Save

### 7.2 Cấu Hình Azure Firewall Rules

#### 7.2.1 Application Rules

1. Vào Azure Firewall > **Rules**
2. Thêm application rule collection:
   - **Name**: `AllowWebTraffic`
   - **Priority**: 100
   - **Action**: Allow

3. Thêm rules:
   - **Name**: `AllowHTTPS`
   - **Source**: `**********/16,**********/16`
   - **Protocol**: HTTPS
   - **Target FQDNs**: `*.microsoft.com,*.azure.com`

#### 7.2.2 Network Rules

1. Thêm network rule collection:
   - **Name**: `AllowDNS`
   - **Priority**: 200
   - **Action**: Allow

2. Thêm rule:
   - **Name**: `AllowDNSTraffic`
   - **Source**: `**********/16,**********/16`
   - **Destination**: `168.63.129.16`
   - **Ports**: 53
   - **Protocol**: UDP

## 📊 Bước 8: Cấu Hình Monitoring

### 8.1 Kết Nối VNets với Log Analytics

1. Vào Log Analytics workspace
2. Click **Virtual machines**
3. Chọn VMs trong spoke networks
4. Click **Connect**

### 8.2 Cấu Hình Diagnostic Settings

#### 8.2.1 Cho Azure Firewall

1. Vào Azure Firewall > **Diagnostic settings**
2. Click **+ Add diagnostic setting**
3. Cấu hình:
   - **Name**: `firewall-diagnostics`
   - **Logs**: Chọn tất cả categories
   - **Destination**: Log Analytics workspace
   - **Workspace**: `law-ewh-infrastructure-sea`

#### 8.2.2 Cho Virtual Networks

Tương tự cấu hình diagnostic settings cho các VNets để gửi logs về Log Analytics.

## ✅ Bước 9: Xác Thực Triển Khai

### 9.1 Kiểm Tra Management Groups

1. Vào **Management groups**
2. Xác nhận cấu trúc hierarchy đã được tạo đúng
3. Kiểm tra subscriptions đã được gán đúng management groups

### 9.2 Kiểm Tra Connectivity

1. Deploy test VMs trong spoke networks
2. Test connectivity giữa spokes qua hub
3. Kiểm tra internet access qua Azure Firewall

### 9.3 Kiểm Tra Monitoring

1. Vào Log Analytics workspace
2. Chạy query để xác nhận logs đang được thu thập:
   ```kusto
   AzureDiagnostics
   | where TimeGenerated > ago(1h)
   | summarize count() by ResourceType
   ```

### 9.4 Kiểm Tra Policies

1. Vào **Policy** > **Compliance**
2. Xác nhận compliance status của các assignments
3. Kiểm tra có resource nào vi phạm policy không

## 🔧 Troubleshooting

### Lỗi Thường Gặp

#### 1. Không thể tạo Management Group
- **Nguyên nhân**: Thiếu quyền
- **Giải pháp**: Xác nhận có quyền Owner ở tenant level

#### 2. VNet Peering thất bại
- **Nguyên nhân**: Address space overlap
- **Giải pháp**: Kiểm tra và điều chỉnh CIDR ranges

#### 3. Azure Firewall không route traffic
- **Nguyên nhân**: Route table chưa được gán đúng
- **Giải pháp**: Kiểm tra route table associations

## 📞 Hỗ Trợ

Để được hỗ trợ:
- **Email**: <EMAIL>
- **Teams**: EWH Platform Team
- **Documentation**: [Internal Wiki](https://wiki.ewh.com/azure-landing-zone)

## 📋 Checklist Hoàn Thành

- [ ] Management Groups được tạo theo đúng hierarchy
- [ ] Subscriptions được gán đúng management groups  
- [ ] Azure Policies được assign và compliant
- [ ] Log Analytics workspaces hoạt động
- [ ] Microsoft Defender for Cloud được cấu hình
- [ ] Hub virtual network và Azure Firewall triển khai
- [ ] Spoke networks và peering hoạt động
- [ ] Network security groups được cấu hình
- [ ] Monitoring và logging hoạt động
- [ ] Test connectivity thành công

---

**Lưu ý**: Tài liệu này dựa trên cấu hình Terraform hiện có và được chuyển đổi để triển khai qua Azure Portal. Một số tính năng nâng cao có thể cần sử dụng Azure CLI hoặc PowerShell để cấu hình.
